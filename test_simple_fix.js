/**
 * اختبار بسيط لفهم مشكلة عرض المحتوى الشامل
 */

const fs = require('fs');

console.log('🔧 اختبار بسيط لمشكلة عرض المحتوى...');

// إنشاء تقرير بسيط لاختبار العرض
function createSimpleTestReport() {
    // محاكاة ثغرة مع بيانات شاملة
    const testVuln = {
        name: 'SQL Injection في نموذج تسجيل الدخول',
        type: 'SQL Injection',
        severity: 'Critical',
        
        // محاكاة النتائج من الدوال الـ36
        comprehensive_details: {
            technical_details: 'تفاصيل تقنية شاملة للثغرة',
            impact_analysis: 'تحليل التأثير المفصل',
            exploitation_results: 'نتائج الاستغلال الحقيقية',
            discovery_process: 'عملية الاكتشاف المفصلة'
        },
        
        dynamic_impact: {
            immediate_impact: 'التأثير الفوري على النظام',
            long_term_consequences: 'العواقب طويلة المدى',
            business_impact: 'التأثير على الأعمال',
            security_implications: 'الآثار الأمنية'
        },
        
        exploitation_steps: [
            'تحديد نقطة الدخول',
            'اختبار الثغرة',
            'تأكيد الاستغلال',
            'توثيق النتائج'
        ],
        
        dynamic_recommendations: {
            immediate_actions: 'إجراءات فورية',
            technical_fixes: 'إصلاحات تقنية',
            prevention_measures: 'إجراءات وقائية',
            monitoring_recommendations: 'توصيات المراقبة'
        }
    };
    
    // دوال تحويل بسيطة
    function convertObjectToHTML(data, title = '') {
        if (!data) return 'لا توجد بيانات';
        
        if (typeof data === 'string') {
            return data.replace(/\n/g, '<br>');
        }
        
        if (typeof data === 'object') {
            let html = '';
            if (title) html += `<h4>${title}</h4>`;
            
            if (Array.isArray(data)) {
                html += '<ul>';
                data.forEach(item => {
                    html += `<li>${item}</li>`;
                });
                html += '</ul>';
            } else {
                html += '<div class="object-content">';
                for (const [key, value] of Object.entries(data)) {
                    html += `<div class="detail-item">`;
                    html += `<strong>${key}:</strong> `;
                    if (typeof value === 'object') {
                        html += convertObjectToHTML(value);
                    } else {
                        html += value;
                    }
                    html += `</div>`;
                }
                html += '</div>';
            }
            
            return html;
        }
        
        return String(data);
    }
    
    // إنشاء HTML للتقرير
    const reportHTML = `
<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تقرير اختبار المحتوى الشامل</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 10px; }
        .vulnerability { border: 1px solid #ddd; margin: 20px 0; padding: 20px; border-radius: 5px; }
        .section { margin: 15px 0; padding: 15px; background: #f8f9fa; border-radius: 5px; }
        .detail-item { margin: 10px 0; padding: 10px; background: #e9ecef; border-radius: 3px; }
        .object-content { margin: 10px 0; }
        h1, h2, h3, h4, h5 { color: #333; }
        .fire-emoji { color: #ff6b35; }
        .check-emoji { color: #28a745; }
        .chart-emoji { color: #007bff; }
        ul { margin: 10px 0; padding-right: 20px; }
        li { margin: 5px 0; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔥 تقرير اختبار المحتوى الشامل التفصيلي</h1>
        
        <div class="vulnerability">
            <h2>🎯 ${testVuln.name}</h2>
            <p><strong>النوع:</strong> ${testVuln.type}</p>
            <p><strong>الخطورة:</strong> ${testVuln.severity}</p>
            
            <div class="section">
                <h3>📋 التفاصيل الشاملة (comprehensive_details)</h3>
                <div class="content">
                    ${convertObjectToHTML(testVuln.comprehensive_details, 'التفاصيل الشاملة')}
                </div>
            </div>
            
            <div class="section">
                <h3>💥 التأثير الديناميكي (dynamic_impact)</h3>
                <div class="content">
                    ${convertObjectToHTML(testVuln.dynamic_impact, 'التأثير الديناميكي')}
                </div>
            </div>
            
            <div class="section">
                <h3>🎯 خطوات الاستغلال (exploitation_steps)</h3>
                <div class="content">
                    ${convertObjectToHTML(testVuln.exploitation_steps, 'خطوات الاستغلال')}
                </div>
            </div>
            
            <div class="section">
                <h3>🔧 التوصيات الديناميكية (dynamic_recommendations)</h3>
                <div class="content">
                    ${convertObjectToHTML(testVuln.dynamic_recommendations, 'التوصيات الديناميكية')}
                </div>
            </div>
        </div>
        
        <div class="section">
            <h3>📊 تحليل النتائج</h3>
            <p>✅ تم عرض جميع البيانات الشاملة بنجاح</p>
            <p>✅ تم تحويل الكائنات إلى HTML منسق</p>
            <p>✅ تم عرض المصفوفات كقوائم</p>
            <p>✅ تم عرض النصوص مع تنسيق مناسب</p>
        </div>
    </div>
</body>
</html>`;
    
    return reportHTML;
}

// إنشاء التقرير وحفظه
const testReport = createSimpleTestReport();
fs.writeFileSync('test_comprehensive_content_display.html', testReport, 'utf8');

console.log('✅ تم إنشاء تقرير اختبار المحتوى الشامل');
console.log('📄 الملف: test_comprehensive_content_display.html');
console.log(`📏 حجم التقرير: ${testReport.length} حرف`);

// تحليل المحتوى
console.log('\n📊 تحليل المحتوى:');
console.log(`🔍 يحتوي على "التفاصيل الشاملة": ${testReport.includes('التفاصيل الشاملة') ? '✅' : '❌'}`);
console.log(`🔍 يحتوي على "التأثير الديناميكي": ${testReport.includes('التأثير الديناميكي') ? '✅' : '❌'}`);
console.log(`🔍 يحتوي على "خطوات الاستغلال": ${testReport.includes('خطوات الاستغلال') ? '✅' : '❌'}`);
console.log(`🔍 يحتوي على "التوصيات الديناميكية": ${testReport.includes('التوصيات الديناميكية') ? '✅' : '❌'}`);

// فحص طول المحتوى المُنتج
const detailsMatch = testReport.match(/<div class="content">[\s\S]*?<\/div>/g);
if (detailsMatch) {
    console.log(`📏 عدد أقسام المحتوى: ${detailsMatch.length}`);
    detailsMatch.forEach((section, index) => {
        console.log(`📏 القسم ${index + 1}: ${section.length} حرف`);
    });
}

console.log('\n💡 هذا يُظهر كيف يجب أن يبدو المحتوى الشامل في التقارير');
console.log('🔥 المشكلة في النظام الحقيقي هي أن الدوال تُرجع كائنات لكن التقارير تتوقع نصوص');
console.log('✅ الحل هو استخدام دوال التحويل المناسبة لكل نوع من البيانات');
