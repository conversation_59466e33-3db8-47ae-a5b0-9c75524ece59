
const fs = require('fs');

try {
    console.log('🔍 اختبار الكود المُصحح...');
    
    const code = fs.readFileSync('assets/modules/bugbounty/BugBountyCore.js', 'utf8');
    
    // فحص أساسي للـ syntax
    const testFunc = new Function('module', 'exports', 'window', 'global', 'console', 'require', code);
    
    console.log('✅ الكود يمر فحص الـ syntax بنجاح');
    
    // محاولة تشغيل الكود
    const mockModule = { exports: {} };
    const mockWindow = {};
    const mockGlobal = {};
    
    testFunc(mockModule, mockModule.exports, mockWindow, mockGlobal, console, require);
    
    console.log('✅ الكود يعمل بدون أخطاء');
    
    // فحص تصدير الكلاس
    if (mockModule.exports || mockWindow.BugBountyCore || mockGlobal.BugBountyCore) {
        console.log('✅ تم تصدير BugBountyCore بنجاح');
    } else {
        console.log('⚠️ لم يتم تصدير BugBountyCore');
    }
    
} catch (error) {
    console.error('❌ خطأ في الكود:', error.message);
    console.error('📍 موقع الخطأ:', error.stack?.split('\n')[1] || 'غير محدد');
}
