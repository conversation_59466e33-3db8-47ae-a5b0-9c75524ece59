
const fs = require('fs');

try {
    const code = fs.readFileSync('assets/modules/bugbounty/BugBountyCore.js', 'utf8');
    
    // محاولة تحليل الكود
    const testFunc = new Function('module', 'exports', 'window', 'global', 'console', 'require', code);
    
    const mockModule = { exports: {} };
    const mockWindow = {};
    const mockGlobal = {};
    
    testFunc(mockModule, mockModule.exports, mockWindow, mockGlobal, console, require);
    
    console.log('✅ الكود يعمل بدون أخطاء syntax');
    
    if (mockModule.exports.name === 'BugBountyCore' || mockWindow.BugBountyCore || mockGlobal.BugBountyCore) {
        console.log('✅ تم تصدير BugBountyCore بنجاح');
    } else {
        console.log('⚠️ لم يتم تصدير BugBountyCore');
    }
    
} catch (error) {
    console.error('❌ خطأ في الكود:', error.message);
}
