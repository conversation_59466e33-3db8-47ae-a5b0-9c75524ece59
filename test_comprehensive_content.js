// اختبار سريع لفحص المحتوى الشامل
const fs = require('fs');

// قراءة ملف BugBountyCore.js
const coreContent = fs.readFileSync('assets/modules/bugbounty/BugBountyCore.js', 'utf8');

// إنشاء instance من BugBountyCore
eval(coreContent);

async function testComprehensiveContent() {
    console.log('🔍 اختبار المحتوى الشامل...');
    
    try {
        const bugBounty = new BugBountyCore();
        
        // إنشاء ثغرة تجريبية
        const testVuln = {
            name: 'SQL Injection في نموذج تسجيل الدخول',
            type: 'SQL Injection',
            severity: 'Critical',
            url: 'https://example.com/login.php',
            parameter: 'username',
            payload: "admin' OR '1'='1' --"
        };
        
        const testRealData = {
            url: 'https://example.com/login.php',
            parameter: 'username',
            payload: "admin' OR '1'='1' --",
            response: 'استجابة تؤكد وجود الثغرة',
            evidence: 'أدلة تؤكد الاستغلال'
        };
        
        console.log('🔥 اختبار دالة generateComprehensiveDetailsFromRealData...');
        const comprehensiveDetails = await bugBounty.generateComprehensiveDetailsFromRealData(testVuln, testRealData);
        
        console.log('📊 نتائج الاختبار:');
        console.log(`📏 نوع النتيجة: ${typeof comprehensiveDetails}`);
        
        if (typeof comprehensiveDetails === 'object' && comprehensiveDetails !== null) {
            console.log('✅ الدالة تُرجع كائن شامل');
            console.log(`📋 الخصائص الموجودة: ${Object.keys(comprehensiveDetails).join(', ')}`);
            
            // فحص المحتوى الشامل
            if (comprehensiveDetails.technical_details) {
                console.log(`📝 طول الوصف الشامل: ${comprehensiveDetails.technical_details.comprehensive_description?.length || 0} حرف`);
                console.log(`📄 أول 200 حرف من الوصف:`);
                console.log(comprehensiveDetails.technical_details.comprehensive_description?.substring(0, 200) || 'لا يوجد محتوى');
            }
            
            // فحص تحليل التأثير
            if (comprehensiveDetails.impact_analysis) {
                console.log(`💥 تحليل التأثير موجود: ✅`);
                console.log(`📏 طول التأثير المفصل: ${comprehensiveDetails.impact_analysis.detailed_impact?.length || 0} حرف`);
            }
            
            // فحص نتائج الاستغلال
            if (comprehensiveDetails.exploitation_results) {
                console.log(`🎯 نتائج الاستغلال موجودة: ✅`);
                console.log(`📏 طول خطوات الاستغلال: ${comprehensiveDetails.exploitation_results.detailed_steps?.length || 0} حرف`);
            }
            
            // فحص الحوارات التفاعلية
            if (comprehensiveDetails.interactive_dialogue) {
                console.log(`💬 الحوارات التفاعلية موجودة: ✅`);
                console.log(`📏 طول الحوار: ${comprehensiveDetails.interactive_dialogue.detailed_conversation?.length || 0} حرف`);
            }
            
            // فحص النتائج المثابرة
            if (comprehensiveDetails.persistent_results) {
                console.log(`🔄 النتائج المثابرة موجودة: ✅`);
                console.log(`📏 طول التحليل المثابر: ${comprehensiveDetails.persistent_results.comprehensive_analysis?.length || 0} حرف`);
            }
            
        } else {
            console.log('❌ الدالة لا تُرجع كائن شامل');
            console.log(`📄 المحتوى المُرجع: ${comprehensiveDetails}`);
        }
        
        console.log('\n🔍 اختبار دالة convertObjectToFormattedHTML...');
        const formattedHTML = bugBounty.convertObjectToFormattedHTML(comprehensiveDetails);
        console.log(`📏 طول HTML المنسق: ${formattedHTML.length} حرف`);
        console.log(`📄 أول 300 حرف من HTML:`);
        console.log(formattedHTML.substring(0, 300));
        
        console.log('\n✅ انتهى الاختبار');
        
    } catch (error) {
        console.error('❌ خطأ في الاختبار:', error.message);
        console.error(error.stack);
    }
}

testComprehensiveContent();
