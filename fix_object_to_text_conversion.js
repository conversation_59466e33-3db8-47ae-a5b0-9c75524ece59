/**
 * إصلاح شامل لمشكلة تحويل الكائنات إلى نصوص في التقارير
 * المشكلة: الدوال الـ36 تُرجع كائنات لكن التقارير تتوقع نصوص
 */

const fs = require('fs');

console.log('🔧 بدء إصلاح مشكلة تحويل الكائنات إلى نصوص...');

// قراءة ملف BugBountyCore.js
let bugBountyCoreContent = fs.readFileSync('assets/modules/bugbounty/BugBountyCore.js', 'utf8');

console.log('📄 تم قراءة ملف BugBountyCore.js');

// إضافة دالة تحويل الكائنات إلى HTML منسق
const objectToHTMLConverter = `
    // 🔥 دالة تحويل الكائنات إلى HTML منسق - إصلاح مشكلة [object Object]
    convertObjectToFormattedHTML(data, title = '') {
        if (!data) return '';
        
        // إذا كان نص بالفعل، أرجعه كما هو
        if (typeof data === 'string') {
            return data.replace(/\\n/g, '<br>').replace(/\\*\\*(.*?)\\*\\*/g, '<strong>$1</strong>');
        }
        
        // إذا كان كائن، حوله إلى HTML منسق
        if (typeof data === 'object') {
            let html = '';
            
            if (title) {
                html += \`<h4>\${title}</h4>\`;
            }
            
            // معالجة الكائنات المختلفة
            if (data.technical_details) {
                html += '<div class="technical-details">';
                html += '<h5>🔬 التفاصيل التقنية</h5>';
                html += this.convertObjectToFormattedHTML(data.technical_details);
                html += '</div>';
            }
            
            if (data.impact_analysis) {
                html += '<div class="impact-analysis">';
                html += '<h5>💥 تحليل التأثير</h5>';
                html += this.convertObjectToFormattedHTML(data.impact_analysis);
                html += '</div>';
            }
            
            if (data.exploitation_results) {
                html += '<div class="exploitation-results">';
                html += '<h5>🎯 نتائج الاستغلال</h5>';
                html += this.convertObjectToFormattedHTML(data.exploitation_results);
                html += '</div>';
            }
            
            if (data.interactive_dialogue) {
                html += '<div class="interactive-dialogue">';
                html += '<h5>💬 الحوار التفاعلي</h5>';
                html += this.convertObjectToFormattedHTML(data.interactive_dialogue);
                html += '</div>';
            }
            
            if (data.visual_changes) {
                html += '<div class="visual-changes">';
                html += '<h5>🎨 التغيرات البصرية</h5>';
                html += this.convertObjectToFormattedHTML(data.visual_changes);
                html += '</div>';
            }
            
            if (data.persistent_results) {
                html += '<div class="persistent-results">';
                html += '<h5>🔄 النتائج المثابرة</h5>';
                html += this.convertObjectToFormattedHTML(data.persistent_results);
                html += '</div>';
            }
            
            // معالجة الخصائص الأخرى
            for (const [key, value] of Object.entries(data)) {
                if (!['technical_details', 'impact_analysis', 'exploitation_results', 'interactive_dialogue', 'visual_changes', 'persistent_results'].includes(key)) {
                    if (typeof value === 'string' && value.length > 10) {
                        html += \`<div class="detail-item"><strong>\${key}:</strong> \${value.replace(/\\n/g, '<br>')}</div>\`;
                    } else if (typeof value === 'object' && value !== null) {
                        html += \`<div class="detail-section"><h6>\${key}:</h6>\${this.convertObjectToFormattedHTML(value)}</div>\`;
                    } else if (Array.isArray(value)) {
                        html += \`<div class="detail-list"><h6>\${key}:</h6><ul>\`;
                        value.forEach(item => {
                            html += \`<li>\${typeof item === 'object' ? this.convertObjectToFormattedHTML(item) : item}</li>\`;
                        });
                        html += '</ul></div>';
                    }
                }
            }
            
            return html;
        }
        
        // إذا كان مصفوفة
        if (Array.isArray(data)) {
            let html = '<ul>';
            data.forEach(item => {
                html += \`<li>\${this.convertObjectToFormattedHTML(item)}</li>\`;
            });
            html += '</ul>';
            return html;
        }
        
        return String(data);
    }

    // 🔥 دالة محسنة لتنسيق التفاصيل الشاملة
    formatComprehensiveDetailsToHTML(details) {
        if (!details) return 'لا توجد تفاصيل متاحة';
        
        // إذا كان كائن، استخدم المحول الجديد
        if (typeof details === 'object') {
            return this.convertObjectToFormattedHTML(details, '📋 التفاصيل الشاملة التفصيلية');
        }
        
        // إذا كان نص، نسقه
        if (typeof details === 'string') {
            return details
                .replace(/\\n/g, '<br>')
                .replace(/\\*\\*(.*?)\\*\\*/g, '<strong>$1</strong>')
                .replace(/\\*(.*?)\\*/g, '<em>$1</em>')
                .replace(/🔥/g, '<span class="fire-emoji">🔥</span>')
                .replace(/✅/g, '<span class="check-emoji">✅</span>')
                .replace(/📊/g, '<span class="chart-emoji">📊</span>');
        }
        
        return String(details);
    }

    // 🔥 دالة محسنة لتنسيق المحتوى الديناميكي
    formatDynamicContentToHTML(content) {
        if (!content) return 'لا يوجد محتوى متاح';
        
        // إذا كان كائن، استخدم المحول الجديد
        if (typeof content === 'object') {
            return this.convertObjectToFormattedHTML(content, '💥 المحتوى الديناميكي');
        }
        
        // إذا كان نص، نسقه
        if (typeof content === 'string') {
            return content
                .replace(/\\n/g, '<br>')
                .replace(/\\*\\*(.*?)\\*\\*/g, '<strong>$1</strong>')
                .replace(/\\*(.*?)\\*/g, '<em>$1</em>')
                .replace(/🔴/g, '<span class="red-emoji">🔴</span>')
                .replace(/📊/g, '<span class="chart-emoji">📊</span>')
                .replace(/💥/g, '<span class="impact-emoji">💥</span>');
        }
        
        return String(content);
    }

    // 🔥 دالة محسنة لتنسيق خطوات الاستغلال
    formatExploitationStepsToHTML(steps) {
        if (!steps) return 'لا توجد خطوات متاحة';
        
        // إذا كان كائن، استخدم المحول الجديد
        if (typeof steps === 'object') {
            return this.convertObjectToFormattedHTML(steps, '🎯 خطوات الاستغلال');
        }
        
        // إذا كان مصفوفة
        if (Array.isArray(steps)) {
            let html = '<ol class="exploitation-steps">';
            steps.forEach((step, index) => {
                html += \`<li class="step-item"><strong>الخطوة \${index + 1}:</strong> \${typeof step === 'object' ? this.convertObjectToFormattedHTML(step) : step}</li>\`;
            });
            html += '</ol>';
            return html;
        }
        
        // إذا كان نص، نسقه
        if (typeof steps === 'string') {
            return steps
                .replace(/\\n/g, '<br>')
                .replace(/\\*\\*(.*?)\\*\\*/g, '<strong>$1</strong>')
                .replace(/\\*(.*?)\\*/g, '<em>$1</em>')
                .replace(/🎯/g, '<span class="target-emoji">🎯</span>')
                .replace(/🔧/g, '<span class="tool-emoji">🔧</span>');
        }
        
        return String(steps);
    }

    // 🔥 دالة محسنة لتنسيق التوصيات
    formatRecommendationsToHTML(recommendations) {
        if (!recommendations) return 'لا توجد توصيات متاحة';
        
        // إذا كان كائن، استخدم المحول الجديد
        if (typeof recommendations === 'object') {
            return this.convertObjectToFormattedHTML(recommendations, '🔧 التوصيات الديناميكية');
        }
        
        // إذا كان نص، نسقه
        if (typeof recommendations === 'string') {
            return recommendations
                .replace(/\\n/g, '<br>')
                .replace(/\\*\\*(.*?)\\*\\*/g, '<strong>$1</strong>')
                .replace(/\\*(.*?)\\*/g, '<em>$1</em>')
                .replace(/🔧/g, '<span class="fix-emoji">🔧</span>')
                .replace(/⚠️/g, '<span class="warning-emoji">⚠️</span>');
        }
        
        return String(recommendations);
    }

    // 🔥 دالة محسنة لتنسيق الحوار التفاعلي
    formatInteractiveDialogueToHTML(dialogue) {
        if (!dialogue) return 'لا يوجد حوار متاح';
        
        // إذا كان كائن، استخدم المحول الجديد
        if (typeof dialogue === 'object') {
            return this.convertObjectToFormattedHTML(dialogue, '💬 الحوار التفاعلي');
        }
        
        // إذا كان نص، نسقه
        if (typeof dialogue === 'string') {
            return dialogue
                .replace(/\\n/g, '<br>')
                .replace(/\\*\\*(.*?)\\*\\*/g, '<strong>$1</strong>')
                .replace(/\\*(.*?)\\*/g, '<em>$1</em>')
                .replace(/💬/g, '<span class="dialogue-emoji">💬</span>')
                .replace(/🔍/g, '<span class="search-emoji">🔍</span>');
        }
        
        return String(dialogue);
    }
`;

// البحث عن مكان إدراج الدوال الجديدة
const insertPosition = bugBountyCoreContent.indexOf('// 🔥 دالة جديدة محسنة: توليد تلقائي ديناميكي للتأثير من الثغرة المكتشفة والمختبرة فعلياً');

if (insertPosition === -1) {
    console.error('❌ لم يتم العثور على نقطة الإدراج المناسبة');
    process.exit(1);
}

// إدراج الدوال الجديدة
const updatedContent = bugBountyCoreContent.slice(0, insertPosition) + 
                      objectToHTMLConverter + 
                      '\n\n    ' + 
                      bugBountyCoreContent.slice(insertPosition);

// حفظ الملف المحدث
fs.writeFileSync('assets/modules/bugbounty/BugBountyCore.js', updatedContent, 'utf8');

console.log('✅ تم إضافة دوال تحويل الكائنات إلى HTML منسق');

// الآن نحتاج لتحديث الدوال التي تستخدم هذه النتائج في التقارير
console.log('🔧 تحديث دوال عرض التقارير...');

// البحث عن الدوال التي تحتاج تحديث
const functionsToUpdate = [
    {
        search: 'formatComprehensiveDetailsToHTML(vuln.comprehensive_details)',
        replace: 'this.formatComprehensiveDetailsToHTML(vuln.comprehensive_details)'
    },
    {
        search: 'formatDynamicContentToHTML(vuln.dynamic_impact)',
        replace: 'this.formatDynamicContentToHTML(vuln.dynamic_impact)'
    },
    {
        search: '${vuln.comprehensive_details && typeof vuln.comprehensive_details === \'string\' && vuln.comprehensive_details.length > 50 ?',
        replace: '${vuln.comprehensive_details ? this.formatComprehensiveDetailsToHTML(vuln.comprehensive_details) :'
    },
    {
        search: '${vuln.dynamic_impact && typeof vuln.dynamic_impact === \'string\' ?',
        replace: '${vuln.dynamic_impact ? this.formatDynamicContentToHTML(vuln.dynamic_impact) :'
    },
    {
        search: '${vuln.exploitation_steps && typeof vuln.exploitation_steps === \'string\' ?',
        replace: '${vuln.exploitation_steps ? this.formatExploitationStepsToHTML(vuln.exploitation_steps) :'
    },
    {
        search: '${vuln.dynamic_recommendations && typeof vuln.dynamic_recommendations === \'string\' ?',
        replace: '${vuln.dynamic_recommendations ? this.formatRecommendationsToHTML(vuln.dynamic_recommendations) :'
    }
];

let finalContent = updatedContent;

functionsToUpdate.forEach((update, index) => {
    const count = (finalContent.match(new RegExp(update.search.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'g')) || []).length;
    if (count > 0) {
        finalContent = finalContent.replace(new RegExp(update.search.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'g'), update.replace);
        console.log(`✅ تم تحديث ${count} مكان للدالة ${index + 1}`);
    } else {
        console.log(`⚠️ لم يتم العثور على الدالة ${index + 1}: ${update.search.substring(0, 50)}...`);
    }
});

// حفظ الملف النهائي
fs.writeFileSync('assets/modules/bugbounty/BugBountyCore.js', finalContent, 'utf8');

console.log('✅ تم حفظ الملف المحدث');
console.log('🎉 تم إصلاح مشكلة تحويل الكائنات إلى نصوص بنجاح!');

console.log('\n📋 ملخص الإصلاحات:');
console.log('1. ✅ إضافة دالة convertObjectToFormattedHTML لتحويل الكائنات إلى HTML منسق');
console.log('2. ✅ تحديث formatComprehensiveDetailsToHTML لدعم الكائنات');
console.log('3. ✅ تحديث formatDynamicContentToHTML لدعم الكائنات');
console.log('4. ✅ إضافة formatExploitationStepsToHTML للخطوات');
console.log('5. ✅ إضافة formatRecommendationsToHTML للتوصيات');
console.log('6. ✅ إضافة formatInteractiveDialogueToHTML للحوارات');

console.log('\n🔥 الآن يجب أن تظهر جميع التفاصيل الشاملة بالكامل في التقارير!');
