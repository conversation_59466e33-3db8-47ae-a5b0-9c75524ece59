/**
 * إصلاح بنية الكود في BugBountyCore.js
 */

const fs = require('fs');

console.log('🔧 بدء إصلاح بنية الكود...');

// قراءة الملف
let content = fs.readFileSync('assets/modules/bugbounty/BugBountyCore.js', 'utf8');

console.log('📄 تم قراءة الملف');

// إصلاح الأقواس المفقودة والفواصل المنقوطة
let fixedContent = content;

// إصلاح 1: إضافة الأقواس المفقودة في نهاية الكلاس
const classEndPattern = /(\n\s*\/\/ [^\n]*\n\s*[a-zA-Z_][a-zA-Z0-9_]*\([^)]*\)\s*\{[^}]*\}\s*)$/;
if (!fixedContent.trim().endsWith('}')) {
    // البحث عن آخر دالة في الكلاس
    const lastFunctionMatch = fixedContent.match(/(\n\s*[a-zA-Z_][a-zA-Z0-9_]*\([^)]*\)\s*\{[\s\S]*?\n\s*\})\s*$/);
    if (lastFunctionMatch) {
        const insertPos = lastFunctionMatch.index + lastFunctionMatch[0].length;
        fixedContent = fixedContent.slice(0, insertPos) + '\n}\n' + fixedContent.slice(insertPos);
        console.log('✅ تم إضافة قوس إغلاق الكلاس');
    }
}

// إصلاح 2: إضافة الفواصل المنقوطة المفقودة
const lines = fixedContent.split('\n');
const fixedLines = [];

for (let i = 0; i < lines.length; i++) {
    const line = lines[i];
    const nextLine = i + 1 < lines.length ? lines[i + 1] : '';
    
    // إذا كان السطر التالي يبدأ بدالة والسطر الحالي لا ينتهي بفاصلة منقوطة أو قوس
    if (nextLine.trim().match(/^[a-zA-Z_][a-zA-Z0-9_]*\s*\([^)]*\)\s*\{/) &&
        line.trim().length > 0 &&
        !line.trim().endsWith(';') && 
        !line.trim().endsWith('}') && 
        !line.trim().endsWith('{') &&
        !line.trim().startsWith('//') &&
        !line.trim().startsWith('*') &&
        !line.trim().startsWith('/*') &&
        !line.includes('class ') &&
        !line.includes('function ')) {
        
        fixedLines.push(line + ';');
        console.log(`🔧 إضافة فاصلة منقوطة للسطر ${i + 1}: ${line.trim().substring(0, 50)}...`);
    } else {
        fixedLines.push(line);
    }
}

fixedContent = fixedLines.join('\n');

// إصلاح 3: إصلاح template literals المكسورة
fixedContent = fixedContent.replace(/\$\{([^}]*)\n([^}]*)\}/g, '${$1 $2}');

// إصلاح 4: إصلاح الأقواس غير المتوازنة في template literals
let braceCount = 0;
let inTemplate = false;
let fixedChars = [];

for (let i = 0; i < fixedContent.length; i++) {
    const char = fixedContent[i];
    const prevChar = i > 0 ? fixedContent[i - 1] : '';
    
    if (char === '`') {
        inTemplate = !inTemplate;
    }
    
    if (inTemplate && char === '{' && prevChar === '$') {
        braceCount++;
    } else if (inTemplate && char === '}' && braceCount > 0) {
        braceCount--;
    }
    
    fixedChars.push(char);
}

// إضافة الأقواس المفقودة إذا لزم الأمر
while (braceCount > 0) {
    fixedChars.push('}');
    braceCount--;
    console.log('🔧 إضافة قوس إغلاق مفقود في template literal');
}

fixedContent = fixedChars.join('');

// إصلاح 5: إصلاح الدوال المكررة أو المكسورة
const functionPattern = /(\s+[a-zA-Z_][a-zA-Z0-9_]*\s*\([^)]*\)\s*\{)/g;
const functions = [];
let match;

while ((match = functionPattern.exec(fixedContent)) !== null) {
    const funcName = match[1].trim().split('(')[0].trim();
    if (functions.includes(funcName)) {
        console.log(`⚠️ دالة مكررة: ${funcName}`);
    } else {
        functions.push(funcName);
    }
}

// إصلاح 6: التأكد من وجود export في النهاية
if (!fixedContent.includes('module.exports') && !fixedContent.includes('export')) {
    fixedContent += '\n\n// تصدير الكلاس\nif (typeof module !== "undefined" && module.exports) {\n    module.exports = BugBountyCore;\n}\nif (typeof window !== "undefined") {\n    window.BugBountyCore = BugBountyCore;\n}\nif (typeof global !== "undefined") {\n    global.BugBountyCore = BugBountyCore;\n}\n';
    console.log('✅ تم إضافة تصدير الكلاس');
}

// حفظ الملف المُصحح
fs.writeFileSync('assets/modules/bugbounty/BugBountyCore.js', fixedContent, 'utf8');

console.log('✅ تم حفظ الملف المُصحح');

// فحص سريع للتأكد من الإصلاح
console.log('\n🔍 فحص سريع للبنية:');

// فحص توازن الأقواس
let openBraces = 0;
let closeBraces = 0;
let openParens = 0;
let closeParens = 0;

for (let char of fixedContent) {
    if (char === '{') openBraces++;
    if (char === '}') closeBraces++;
    if (char === '(') openParens++;
    if (char === ')') closeParens++;
}

console.log(`📊 الأقواس المجعدة: ${openBraces} فتح، ${closeBraces} إغلاق - ${openBraces === closeBraces ? '✅ متوازن' : '❌ غير متوازن'}`);
console.log(`📊 الأقواس العادية: ${openParens} فتح، ${closeParens} إغلاق - ${openParens === closeParens ? '✅ متوازن' : '❌ غير متوازن'}`);

// فحص وجود الكلاس
const hasClass = fixedContent.includes('class BugBountyCore');
const hasConstructor = fixedContent.includes('constructor(');
const hasExport = fixedContent.includes('module.exports') || fixedContent.includes('window.BugBountyCore');

console.log(`📊 يحتوي على كلاس BugBountyCore: ${hasClass ? '✅' : '❌'}`);
console.log(`📊 يحتوي على constructor: ${hasConstructor ? '✅' : '❌'}`);
console.log(`📊 يحتوي على تصدير: ${hasExport ? '✅' : '❌'}`);

// فحص الدوال المهمة
const importantFunctions = [
    'formatComprehensiveDetailsToHTML',
    'formatDynamicContentToHTML',
    'formatExploitationStepsToHTML',
    'formatRecommendationsToHTML',
    'convertAnyDataToHTML'
];

console.log('\n📊 فحص الدوال المهمة:');
importantFunctions.forEach(func => {
    const exists = fixedContent.includes(func);
    console.log(`   ${func}: ${exists ? '✅' : '❌'}`);
});

console.log('\n🎉 تم إصلاح بنية الكود بنجاح!');

// إنشاء ملف اختبار بسيط للتحقق
const testCode = `
const fs = require('fs');

try {
    const code = fs.readFileSync('assets/modules/bugbounty/BugBountyCore.js', 'utf8');
    
    // محاولة تحليل الكود
    const testFunc = new Function('module', 'exports', 'window', 'global', 'console', 'require', code);
    
    const mockModule = { exports: {} };
    const mockWindow = {};
    const mockGlobal = {};
    
    testFunc(mockModule, mockModule.exports, mockWindow, mockGlobal, console, require);
    
    console.log('✅ الكود يعمل بدون أخطاء syntax');
    
    if (mockModule.exports.name === 'BugBountyCore' || mockWindow.BugBountyCore || mockGlobal.BugBountyCore) {
        console.log('✅ تم تصدير BugBountyCore بنجاح');
    } else {
        console.log('⚠️ لم يتم تصدير BugBountyCore');
    }
    
} catch (error) {
    console.error('❌ خطأ في الكود:', error.message);
}
`;

fs.writeFileSync('test_code_structure.js', testCode, 'utf8');
console.log('📄 تم إنشاء ملف اختبار البنية: test_code_structure.js');
