/**
 * إصلاح أخطاء بناء الجملة في BugBountyCore.js
 */

const fs = require('fs');

console.log('🔧 بدء إصلاح أخطاء بناء الجملة...');

// قراءة الملف
let content = fs.readFileSync('assets/modules/bugbounty/BugBountyCore.js', 'utf8');

console.log('📄 تم قراءة الملف');

// إصلاح المشاكل الشائعة
const fixes = [
    // إصلاح مشكلة الفاصلة المنقوطة المفقودة قبل الدوال
    {
        search: /(\n    \/\/ [^{]+\n    [a-zA-Z_][a-zA-Z0-9_]*\([^)]*\) \{)/g,
        replace: (match, p1) => {
            // التحقق من وجود فاصلة منقوطة قبل التعليق
            const lines = p1.split('\n');
            if (lines.length >= 3) {
                const prevLine = lines[lines.length - 3];
                if (prevLine && !prevLine.trim().endsWith(';') && !prevLine.trim().endsWith('}') && !prevLine.trim().endsWith('{')) {
                    lines[lines.length - 3] = prevLine + ';';
                    return lines.join('\n');
                }
            }
            return p1;
        }
    },
    
    // إصلاح مشكلة الدوال التي تبدأ بدون فاصلة منقوطة
    {
        search: /(\n    [a-zA-Z_][a-zA-Z0-9_]*\([^)]*\) \{)/g,
        replace: (match, p1) => {
            // البحث عن السطر السابق
            const beforeMatch = content.substring(0, content.indexOf(match));
            const lines = beforeMatch.split('\n');
            const lastLine = lines[lines.length - 1];
            
            if (lastLine && !lastLine.trim().endsWith(';') && !lastLine.trim().endsWith('}') && !lastLine.trim().endsWith('{') && lastLine.trim().length > 0) {
                return ';' + p1;
            }
            return p1;
        }
    }
];

// تطبيق الإصلاحات
fixes.forEach((fix, index) => {
    if (typeof fix.replace === 'function') {
        content = content.replace(fix.search, fix.replace);
    } else {
        content = content.replace(fix.search, fix.replace);
    }
    console.log(`✅ تم تطبيق الإصلاح ${index + 1}`);
});

// إصلاحات يدوية محددة
const manualFixes = [
    // إصلاح الدوال المحددة التي تحتاج فاصلة منقوطة
    {
        search: '    generateRealExploitationToolsForVulnerability(vuln) {',
        replace: '    generateRealExploitationToolsForVulnerability(vuln) {'
    },
    {
        search: '    generateRealExploitationStepsForVulnerability(vuln, exploitationResults = {}) {',
        replace: '    generateRealExploitationStepsForVulnerability(vuln, exploitationResults = {}) {'
    },
    {
        search: '    generateRealExploitationStepsForVulnerabilitySimple(vuln) {',
        replace: '    generateRealExploitationStepsForVulnerabilitySimple(vuln) {'
    },
    {
        search: '    generateRealImpactChangesForVulnerability(vuln) {',
        replace: '    generateRealImpactChangesForVulnerability(vuln) {'
    },
    {
        search: '    convertObjectToFormattedHTML(data, title = \'\') {',
        replace: '    convertObjectToFormattedHTML(data, title = \'\') {'
    },
    {
        search: '    formatComprehensiveDetailsToHTML(details) {',
        replace: '    formatComprehensiveDetailsToHTML(details) {'
    },
    {
        search: '    formatDynamicContentToHTML(content) {',
        replace: '    formatDynamicContentToHTML(content) {'
    },
    {
        search: '    formatExploitationStepsToHTML(steps) {',
        replace: '    formatExploitationStepsToHTML(steps) {'
    },
    {
        search: '    formatRecommendationsToHTML(recommendations) {',
        replace: '    formatRecommendationsToHTML(recommendations) {'
    },
    {
        search: '    formatInteractiveDialogueToHTML(dialogue) {',
        replace: '    formatInteractiveDialogueToHTML(dialogue) {'
    }
];

// تطبيق الإصلاحات اليدوية
manualFixes.forEach((fix, index) => {
    if (content.includes(fix.search)) {
        console.log(`✅ تم العثور على الدالة ${index + 1} وهي صحيحة`);
    } else {
        console.log(`⚠️ لم يتم العثور على الدالة ${index + 1}: ${fix.search.substring(0, 50)}...`);
    }
});

// البحث عن الأسطر التي تحتاج فاصلة منقوطة قبل الدوال
const lines = content.split('\n');
let fixedLines = [];
let needsFix = false;

for (let i = 0; i < lines.length; i++) {
    const line = lines[i];
    const nextLine = i + 1 < lines.length ? lines[i + 1] : '';
    
    // إذا كان السطر التالي يبدأ بدالة والسطر الحالي لا ينتهي بفاصلة منقوطة أو قوس
    if (nextLine.trim().match(/^\/\/ .+/) && 
        i + 2 < lines.length && 
        lines[i + 2].trim().match(/^[a-zA-Z_][a-zA-Z0-9_]*\([^)]*\) \{/) &&
        line.trim().length > 0 &&
        !line.trim().endsWith(';') && 
        !line.trim().endsWith('}') && 
        !line.trim().endsWith('{') &&
        !line.trim().startsWith('//') &&
        !line.trim().startsWith('*') &&
        !line.trim().startsWith('/*')) {
        
        fixedLines.push(line + ';');
        needsFix = true;
        console.log(`🔧 إضافة فاصلة منقوطة للسطر ${i + 1}: ${line.trim()}`);
    } else {
        fixedLines.push(line);
    }
}

if (needsFix) {
    content = fixedLines.join('\n');
    console.log('✅ تم إصلاح الفواصل المنقوطة المفقودة');
}

// حفظ الملف المُصحح
fs.writeFileSync('assets/modules/bugbounty/BugBountyCore.js', content, 'utf8');

console.log('✅ تم حفظ الملف المُصحح');
console.log('🎉 تم إصلاح أخطاء بناء الجملة بنجاح!');

// التحقق من وجود أخطاء أخرى
console.log('\n🔍 فحص سريع للتأكد من الإصلاح...');

try {
    // محاولة تحليل الملف كـ JavaScript (فحص أساسي)
    const testContent = content.substring(0, 10000); // فحص أول 10000 حرف
    
    // فحص الأقواس المتوازنة
    let braceCount = 0;
    let parenCount = 0;
    
    for (let char of testContent) {
        if (char === '{') braceCount++;
        if (char === '}') braceCount--;
        if (char === '(') parenCount++;
        if (char === ')') parenCount--;
    }
    
    console.log(`📊 توازن الأقواس المجعدة: ${braceCount === 0 ? '✅ متوازن' : '❌ غير متوازن'}`);
    console.log(`📊 توازن الأقواس العادية: ${parenCount === 0 ? '✅ متوازن' : '❌ غير متوازن'}`);
    
    console.log('✅ الفحص السريع مكتمل');
    
} catch (error) {
    console.error('❌ خطأ في الفحص:', error.message);
}
