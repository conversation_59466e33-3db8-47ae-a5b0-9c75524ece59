/**
 * الإصلاح النهائي الشامل لمشكلة عرض المحتوى التفصيلي في التقارير
 * هذا الإصلاح يحل المشكلة الجذرية: تحويل الكائنات إلى HTML منسق
 */

const fs = require('fs');

console.log('🔥 بدء الإصلاح النهائي الشامل لمشكلة عرض المحتوى التفصيلي...');

// قراءة ملف BugBountyCore.js
let content = fs.readFileSync('assets/modules/bugbounty/BugBountyCore.js', 'utf8');

console.log('📄 تم قراءة ملف BugBountyCore.js');

// إنشاء دوال التحويل المحسنة
const enhancedConverterFunctions = `
    // 🔥 دالة تحويل شاملة محسنة للكائنات إلى HTML منسق
    convertAnyDataToHTML(data, title = '', maxDepth = 3, currentDepth = 0) {
        if (!data || currentDepth > maxDepth) return '';
        
        // إذا كان نص، نسقه وأرجعه
        if (typeof data === 'string') {
            return data
                .replace(/\\n/g, '<br>')
                .replace(/\\*\\*(.*?)\\*\\*/g, '<strong>$1</strong>')
                .replace(/\\*(.*?)\\*/g, '<em>$1</em>')
                .replace(/🔥/g, '<span style="color: #ff6b35;">🔥</span>')
                .replace(/✅/g, '<span style="color: #28a745;">✅</span>')
                .replace(/📊/g, '<span style="color: #007bff;">📊</span>')
                .replace(/💥/g, '<span style="color: #dc3545;">💥</span>')
                .replace(/🎯/g, '<span style="color: #ffc107;">🎯</span>');
        }
        
        // إذا كان رقم أو boolean
        if (typeof data === 'number' || typeof data === 'boolean') {
            return String(data);
        }
        
        // إذا كان مصفوفة
        if (Array.isArray(data)) {
            let html = '<ul style="margin: 10px 0; padding-right: 20px;">';
            data.forEach((item, index) => {
                html += \`<li style="margin: 5px 0;">\`;
                if (typeof item === 'object') {
                    html += this.convertAnyDataToHTML(item, '', maxDepth, currentDepth + 1);
                } else {
                    html += this.convertAnyDataToHTML(item, '', maxDepth, currentDepth + 1);
                }
                html += '</li>';
            });
            html += '</ul>';
            return html;
        }
        
        // إذا كان كائن
        if (typeof data === 'object' && data !== null) {
            let html = '';
            
            if (title && currentDepth === 0) {
                html += \`<h4 style="color: #333; margin: 15px 0 10px 0;">\${title}</h4>\`;
            }
            
            html += '<div style="margin: 10px 0; padding: 15px; background: #f8f9fa; border-radius: 5px; border-right: 4px solid #007bff;">';
            
            // معالجة الخصائص المعروفة أولاً
            const knownProperties = [
                'technical_details', 'impact_analysis', 'exploitation_results', 
                'discovery_process', 'immediate_impact', 'long_term_consequences',
                'business_impact', 'security_implications', 'immediate_actions',
                'technical_fixes', 'prevention_measures', 'monitoring_recommendations'
            ];
            
            knownProperties.forEach(prop => {
                if (data[prop]) {
                    html += \`<div style="margin: 10px 0; padding: 10px; background: #e9ecef; border-radius: 3px;">\`;
                    html += \`<strong style="color: #495057;">\${this.formatPropertyName(prop)}:</strong><br>\`;
                    html += this.convertAnyDataToHTML(data[prop], '', maxDepth, currentDepth + 1);
                    html += '</div>';
                }
            });
            
            // معالجة باقي الخصائص
            for (const [key, value] of Object.entries(data)) {
                if (!knownProperties.includes(key) && value !== null && value !== undefined) {
                    html += \`<div style="margin: 10px 0; padding: 10px; background: #e9ecef; border-radius: 3px;">\`;
                    html += \`<strong style="color: #495057;">\${this.formatPropertyName(key)}:</strong><br>\`;
                    html += this.convertAnyDataToHTML(value, '', maxDepth, currentDepth + 1);
                    html += '</div>';
                }
            }
            
            html += '</div>';
            return html;
        }
        
        return String(data);
    }
    
    // 🔥 دالة تنسيق أسماء الخصائص
    formatPropertyName(propName) {
        const translations = {
            'technical_details': '🔬 التفاصيل التقنية',
            'impact_analysis': '💥 تحليل التأثير',
            'exploitation_results': '🎯 نتائج الاستغلال',
            'discovery_process': '🔍 عملية الاكتشاف',
            'immediate_impact': '⚡ التأثير الفوري',
            'long_term_consequences': '📈 العواقب طويلة المدى',
            'business_impact': '💼 التأثير على الأعمال',
            'security_implications': '🛡️ الآثار الأمنية',
            'immediate_actions': '🚨 إجراءات فورية',
            'technical_fixes': '🔧 إصلاحات تقنية',
            'prevention_measures': '🛡️ إجراءات وقائية',
            'monitoring_recommendations': '📊 توصيات المراقبة'
        };
        
        return translations[propName] || propName.replace(/_/g, ' ');
    }
    
    // 🔥 دالة محسنة لتنسيق التفاصيل الشاملة
    formatComprehensiveDetailsToHTML(details) {
        if (!details) return '<p style="color: #6c757d; font-style: italic;">لا توجد تفاصيل شاملة متاحة</p>';
        return this.convertAnyDataToHTML(details, '📋 التفاصيل الشاملة التفصيلية');
    }
    
    // 🔥 دالة محسنة لتنسيق التأثير الديناميكي
    formatDynamicContentToHTML(content) {
        if (!content) return '<p style="color: #6c757d; font-style: italic;">لا يوجد تأثير ديناميكي متاح</p>';
        return this.convertAnyDataToHTML(content, '💥 التأثير الديناميكي');
    }
    
    // 🔥 دالة محسنة لتنسيق خطوات الاستغلال
    formatExploitationStepsToHTML(steps) {
        if (!steps) return '<p style="color: #6c757d; font-style: italic;">لا توجد خطوات استغلال متاحة</p>';
        
        if (Array.isArray(steps)) {
            let html = '<div style="margin: 10px 0;"><h4 style="color: #333;">🎯 خطوات الاستغلال</h4>';
            html += '<ol style="margin: 10px 0; padding-right: 20px; background: #f8f9fa; padding: 15px; border-radius: 5px;">';
            steps.forEach((step, index) => {
                html += \`<li style="margin: 8px 0; padding: 5px; background: #e9ecef; border-radius: 3px;">\`;
                html += \`<strong>الخطوة \${index + 1}:</strong> \`;
                html += this.convertAnyDataToHTML(step);
                html += '</li>';
            });
            html += '</ol></div>';
            return html;
        }
        
        return this.convertAnyDataToHTML(steps, '🎯 خطوات الاستغلال');
    }
    
    // 🔥 دالة محسنة لتنسيق التوصيات
    formatRecommendationsToHTML(recommendations) {
        if (!recommendations) return '<p style="color: #6c757d; font-style: italic;">لا توجد توصيات متاحة</p>';
        return this.convertAnyDataToHTML(recommendations, '🔧 التوصيات الديناميكية');
    }
    
    // 🔥 دالة محسنة لتنسيق الحوار التفاعلي
    formatInteractiveDialogueToHTML(dialogue) {
        if (!dialogue) return '<p style="color: #6c757d; font-style: italic;">لا يوجد حوار تفاعلي متاح</p>';
        
        // إذا كان نص HTML بالفعل، أرجعه كما هو
        if (typeof dialogue === 'string' && dialogue.includes('<div')) {
            return dialogue;
        }
        
        return this.convertAnyDataToHTML(dialogue, '💬 الحوار التفاعلي');
    }
`;

// البحث عن مكان إدراج الدوال الجديدة
const insertPosition = content.indexOf('// 🔥 دالة تحويل الكائنات إلى HTML منسق - إصلاح مشكلة [object Object]');

if (insertPosition === -1) {
    console.error('❌ لم يتم العثور على نقطة الإدراج المناسبة');
    process.exit(1);
}

// استبدال الدوال القديمة بالدوال المحسنة
const endPosition = content.indexOf('// 🔥 دالة جديدة محسنة: توليد تلقائي ديناميكي للتأثير من الثغرة المكتشفة والمختبرة فعلياً');

if (endPosition === -1) {
    console.error('❌ لم يتم العثور على نقطة النهاية');
    process.exit(1);
}

// استبدال القسم القديم بالدوال المحسنة
const updatedContent = content.slice(0, insertPosition) + 
                      enhancedConverterFunctions + 
                      '\n\n    ' + 
                      content.slice(endPosition);

// حفظ الملف المحدث
fs.writeFileSync('assets/modules/bugbounty/BugBountyCore.js', updatedContent, 'utf8');

console.log('✅ تم استبدال دوال التحويل بالدوال المحسنة');

console.log('\n📋 ملخص الإصلاحات المطبقة:');
console.log('1. ✅ دالة convertAnyDataToHTML شاملة لجميع أنواع البيانات');
console.log('2. ✅ دالة formatPropertyName لترجمة أسماء الخصائص');
console.log('3. ✅ دوال تنسيق محسنة لكل نوع من المحتوى');
console.log('4. ✅ دعم التداخل المحدود لتجنب التكرار اللانهائي');
console.log('5. ✅ تنسيق CSS مدمج لعرض أفضل');
console.log('6. ✅ معالجة خاصة للحوارات التفاعلية HTML');

console.log('\n🎉 تم الإصلاح النهائي الشامل بنجاح!');
console.log('🔥 الآن يجب أن تظهر جميع التفاصيل الشاملة بالكامل في التقارير!');
